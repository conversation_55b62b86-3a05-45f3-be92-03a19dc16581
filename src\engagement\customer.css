* {
 margin: 0;
 padding: 0;
 box-sizing: border-box;
}

body {
 background-color: #ffffff;
 color: #292b33;
 overflow-x: hidden; /* Prevent horizontal scroll */
 width: 100%;
}

main {
 width: 100%;
 overflow: hidden;
}

/* Container styles */
.container {
 max-width: 1440px;
 margin: 0 auto;
 padding: 0 20px;
}

/* Initial Section */
.initial-section {
 height: 100vh;
 display: flex;
 align-items: center;
 justify-content: center;
 padding: 64px 20px;
}

.text-container {
 max-width: 1290px;
 margin: 0 auto;
 text-align: center;
}

.main-text {
 font-size: clamp(36px, 5vw, 50px);
 line-height: 1.1;
 text-align: center;
 margin-bottom: 50px;
}

.main-text .bold {
 font-weight: 700;
 line-height: 1.4;
 display: inline-block;
}

/* Media queries for main-text in small screens */
@media (max-width: 800px) {
 .main-text {
  max-width: 100%;
  padding: 0 15px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
 }
}

/* Journey Section */
.journey-section {
 max-width: 1141px;
 margin: 0 auto;
 padding: 0 20px 64px;
}

.journey-text {
 margin-bottom: 50px;
 font-family: Raleway;
 font-size: 20px;
 font-style: normal;
 font-weight: 400;
 line-height: 26px;
}

.journey-text p {
 margin-bottom: 50px;
}

.journey-text p:last-child {
 margin-bottom: 0;
}

.journey-text strong {
 font-weight: 700;
}

/* Journey Cards */
.journey-cards {
 display: grid;
 grid-template-columns: repeat(3, 1fr);
 gap: 20px;
}

/* Media queries for journey cards */
@media (max-width: 1200px) {
 .journey-cards {
  grid-template-columns: repeat(2, 1fr);
 }
}

@media (max-width: 1000px) {
 .journey-cards {
  grid-template-columns: repeat(1, 1fr);
  max-width: 600px;
  margin: 0 auto;
 }

 .journey-card {
  height: 211px;
 }
}

.journey-card {
 height: 211px;
 position: relative;
 color: #ffffff;
 perspective: 1000px;
 cursor: pointer;
}

.card-inner {
 position: relative;
 width: 100%;
 height: 100%;
 text-align: left;
 transition: transform 0.8s;
 transform-style: preserve-3d;
 border-radius: 10px;
}

.journey-card.flipped .card-inner {
 transform: rotateY(180deg);
}

.card-front,
.card-back {
 display: flex;
 flex-direction: column;
 justify-content: space-between;
 position: absolute;
 width: 100%;
 height: 100%;
 padding: 15px 18px;
 border-radius: 10px;
 -webkit-backface-visibility: hidden;
 backface-visibility: hidden;
 background-color: inherit;
}

.card-front {
 z-index: 2;
 display: flex;
 flex-direction: column;
 justify-content: space-between;
}

.card-back {
 transform: rotateY(180deg);
 display: flex;
 flex-direction: column;
}

.card-number {
 font-style: italic;
 font-size: 25px;
 line-height: 0.95;
}

.card-title {
 margin-bottom: 15px;
}

.card-title h3 {
 font-size: 47px;
 line-height: 1.1;
 font-weight: 400;
}

.card-line {
 width: 100%;
 height: 1px;
 background-color: #ffffff;
}

.card-back-content {
 font-size: 20px;
 line-height: 1.5;
 font-family: "Raleway";  
}

/* Journey Conclusion */
.journey-conclusion {
 margin-top: 50px;
 font-size: 20px;
 line-height: 1.3;
 color: #292b33;
}

.journey-conclusion p {
 margin-bottom: 20px;
}

.journey-conclusion p:last-child {
 margin-bottom: 0;
}

/* Detailed Sections */
.detailed-section {
 width: 100%;
 margin: 0 auto;
 padding: 64px 0;
 position: relative;
}

.section-header {
 margin-bottom: 60px;
 padding: 0 20px;
 max-width: 1440px;
 margin: 0 auto;
 display: flex;
 flex-wrap: nowrap;
 align-items: baseline;
 width: 100%;
 box-sizing: border-box;
}

.section-number {
 font-style: italic;
 font-size: clamp(35px, 5.5vw, 95px);
 line-height: 1;
 margin-right: 20px;
 flex-shrink: 0;
}

.section-title {
 font-size: clamp(35px, 5.5vw, 95px);
 line-height: 1;
 margin-bottom: 40px;
 flex: 1;
 white-space: normal;
}

.section-subtitle {
 font-size: clamp(22px, 2.5vw, 26px);
 line-height: 1.23;
 font-weight: 700;
 margin-bottom: 60px;
 width: 100%;
}

.section-content {
 max-width: 1440px;
 margin: 0 auto;
 padding: 0 20px;
 font-size: clamp(18px, 2vw, 20px);
 line-height: 1.3;
}

.section-content > *:not(.early-access-banner) {
 max-width: 1140px;
 margin-left: auto;
 margin-right: auto;
}

.section-content p {
 margin-bottom: 30px;
}

.section-question {
 font-size: clamp(28px, 3.5vw, 35px);
 line-height: 1.17;
 text-align: center;
 margin: 60px auto;
 max-width: 1140px;
}

.section-content ul {
 list-style: none;
 margin: 0 0 30px 20px;
}

.section-content ul li {
 position: relative;
 padding-left: 20px;
 margin-bottom: 15px;
}

.section-content ul li:before {
 content: "";
 position: absolute;
 left: 0;
 top: 10px;
 width: 6px;
 height: 6px;
 border-radius: 50%;
 background-color: #292b33;
}

/* Images Grid */
.images-grid {
 display: grid;
 grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
 gap: 20px;
 margin: 60px auto;
 max-width: 1140px;
 padding: 0 20px;
}

.images-grid.images-grid-2 {
 grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
 max-width: 800px;
}

.images-grid img {
 width: 100%;
 height: clamp(200px, 30vw, 283px);
 object-fit: cover;
 border-radius: 10px;
}

/* Final Section */
.final-section {
 padding: 64px 20px;
}

.final-card {
 background: #ffffff;
 border-radius: clamp(49px, 5vw, 98px);
 padding: clamp(100px, 15vw, 202px) 0 64px;
 text-align: center;
 box-shadow: 0px -5px 20px rgba(0, 0, 0, 0.15);
}

.final-card h2 {
 font-size: clamp(36px, 5vw, 50px);
 line-height: 1.1;
 max-width: 1290px;
 margin: 0 auto;
 padding: 0 20px;
}

.final-card .bold {
 font-weight: 700;
 line-height: 70px;
}

/* Accent Elements */
.accent-box {
 position: absolute;
 background-color: #d8bb93;
 width: 136px;
 height: 277px;
 cursor: pointer;
 transition: all 0.5s ease;
 right: 0;
 top: 0;
 border-radius: 10px 0 0 10px;
 display: flex;
 align-items: center;
 justify-content: center;
 overflow: hidden;
 z-index: 100;
}

.accent-box img {
 width: 60px;
 height: 60px;
 transition: all 0.3s ease;
}

.accent-box .left-arrow {
 opacity: 1;
 position: relative;
 z-index: 2;
}

.accent-box .right-arrow {
 opacity: 0;
 position: absolute;
 right: 40px;
 top: 50%;
 transform: translateY(-50%);
 transition: all 0.3s ease;
 z-index: 2;
}

.accent-box.active {
 width: min(90vw, 1000px);
 min-height: 277px;
 height: auto;
 padding: 60px 60px 60px 120px;
 display: flex;
 flex-direction: column;
 justify-content: center;
 position: absolute;
 overflow: visible;
}

.accent-box.active .left-arrow {
 opacity: 0;
 display: none;
}

.accent-box.active .right-arrow {
 opacity: 1;
 position: absolute;
 top: 50%;
 left: 10px;
 transform: translateY(-50%);
}

.expanded-content {
 display: none;
 color: #ffffff;
 opacity: 0;
 transition: opacity 0.3s ease;
 width: 100%;
 max-width: 800px;
}

.accent-box.active .expanded-content {
 display: block;
 opacity: 1;
 width: 100%;
}

.expanded-content h3 {
 font-size: 35px;
 line-height: 1.17;
 font-weight: 400;
 margin-bottom: 25px;
 color: #ffffff;
 word-wrap: break-word;
}

.expanded-content p {
 font-size: 26px;
 line-height: 1.23;
 font-weight: 400;
 color: #ffffff;
 margin: 0;
 word-wrap: break-word;
}

/* Position the accent box relative to the journey card */
.journey-card:first-child {
 position: relative;
}

/* Media queries for accent box */
@media (max-width: 1200px) {
 .accent-box.active {
  width: min(90vw, 900px);
  padding: 50px 50px 50px 100px;
 }

 .expanded-content h3 {
  font-size: 32px;
 }

 .expanded-content p {
  font-size: 24px;
 }
}

@media (max-width: 991px) {
 .accent-box {
  height: 240px;
 }

 .accent-box.active {
  width: min(90vw, 700px);
  padding: 40px 40px 40px 80px;
 }

 .expanded-content h3 {
  font-size: 28px;
  margin-bottom: 20px;
 }

 .expanded-content p {
  font-size: 22px;
 }
}

@media (max-width: 767px) {
 .accent-box {
  height: 200px;
  width: 120px;
 }

 .accent-box.active {
  width: min(95vw, 600px);
  padding: 30px 30px 30px 80px;
 }

 .accent-box img {
  width: 40px;
  height: 40px;
 }

 .expanded-content h3 {
  font-size: 24px;
  margin-bottom: 15px;
 }

 .expanded-content p {
  font-size: 20px;
 }
}

@media (max-width: 480px) {
 .accent-box {
  height: 180px;
  width: 100px;
 }

 .accent-box.active {
  width: min(95vw, 400px);
  padding: 25px 25px 25px 75px;
 }

 .accent-box img {
  width: 35px;
  height: 35px;
 }

 .expanded-content h3 {
  font-size: 22px;
  margin-bottom: 12px;
 }

 .expanded-content p {
  font-size: 18px;
  line-height: 1.4;
 }
}

/* Add overlay when accent box is active */
.accent-box-overlay {
 display: none;
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background: rgba(0, 0, 0, 0.5);
 z-index: 999;
}

.accent-box-overlay.active {
 display: block;
}

/* Star Badge */
.star-badge {
 position: absolute;
 width: 209px;
 height: 205px;
 display: flex;
 align-items: center;
 justify-content: center;
 color: #ffffff;
 text-align: center;
 cursor: pointer;
 z-index: 10; /* Ensure star badge is above other elements */
}

/* Specific positioning for section 04 (top position) */
.detailed-section:nth-child(4) .star-badge {
 top: -40px;
 right: 95px;
 z-index: 10;
 width: 209px;
 height: 205px;
}

/* Default position for other sections */
.detailed-section:not(:nth-child(4)) .star-badge {
 right: 150px;
 top: 83px;
}

.star-badge img {
 position: absolute;
 width: 100%;
 height: 100%;
 z-index: 0;
}

.star-badge-content {
 position: relative;
 z-index: 1;
}

.star-badge-number {
 font-weight: 700;
 font-size: 55px;
 line-height: 76px;
}

.star-badge-text {
 font-size: 16px;
 line-height: 16px;
}

/* Section-specific styles to match Figma */
.detailed-section:nth-child(4) .section-title {
 font-family: "Playfair Display", serif;
}

.detailed-section:nth-child(4) .section-question {
 font-size: 35px;
 line-height: 1.4;
 font-weight: 500;
 text-align: center;
 margin: 80px auto 60px;
 max-width: 850px;
 color: #292b33;
 padding: 0 20px;
}

.detailed-section:nth-child(4) .section-question .key-text {
 font-size: 26px;
 font-style: normal;
 font-weight: 700;
 line-height: 32px;
 margin-bottom: 16px;
}

.detailed-section:nth-child(4) .section-question .main-text {
 font-size: 35px;
 font-style: normal;
 font-weight: 400;
 line-height: normal;
}

.detailed-section:nth-child(4) .section-question em {
 font-family: "Playfair Display";
 font-size: 35px;
 font-style: italic;
 font-weight: 700;
 line-height: normal;
}

.detailed-section:nth-child(4) .section-content p {
 font-size: 20px;
 line-height: 1.6;
 color: #292b33;
 margin-bottom: 30px;
}

.detailed-section:nth-child(4) .section-content ul {
 list-style: none;
 margin: 0 0 40px 0;
 padding: 0;
}

.detailed-section:nth-child(4) .section-content ul li {
 font-size: 20px;
 line-height: 1.6;
 color: #292b33;
 margin-bottom: 12px;
 padding-left: 20px;
 position: relative;
}

.detailed-section:nth-child(4) .section-content ul li:before {
 content: "";
 position: absolute;
 left: 0;
 top: 12px;
 width: 6px;
 height: 6px;
 border-radius: 50%;
 background-color: #292b33;
}

/* For the image in the participation section */
.detailed-section:nth-child(4) .images-grid {
 margin-top: 0;
 position: relative;
 margin-bottom: 30px;
 max-width: 100%;
 width: 100%;
 margin-left: auto;
 margin-right: auto;
}

.detailed-section:nth-child(4) .images-grid img {
 border-radius: 0;
 height: auto;
 width: 100%;
 display: block;
}

.detailed-section:nth-child(4) .content-with-badge {
 position: relative;
 width: 100%;
 max-width: 100%;
 margin: 50px 0 60px;
 padding: 0;
}

/* Star badge in Participation section */
.detailed-section:nth-child(4) .star-badge {
 position: absolute;
 top: -20px;
 right: 50px;
 z-index: 10;
 width: 170px;
 height: 170px;
 transform: none;
 margin-bottom: 0;
}

/* Position subtitle over the image */
.detailed-section:nth-child(4) .section-subtitle {
 font-weight: 600;
 font-size: 65px;
 text-align: center;
 margin: 0 auto;
 color: white;
 position: absolute;
 bottom: 50px;
 left: 0;
 right: 0;
 width: 100%;
 z-index: 5;
 text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* Star badge popup for section 04 should use blue */
.popup-number.blue {
 color: #7ab7d8;
}

@media (max-width: 991px) {
 .detailed-section:nth-child(4) .star-badge {
  right: 30px;
  top: -40px;
 }
}

@media (max-width: 767px) {
 .detailed-section:nth-child(4) .star-badge {
  right: 15px;
  top: -40px;
  width: 180px;
  height: 180px;
 }
}

@media (max-width: 480px) {
 .detailed-section:nth-child(4) .star-badge {
  right: auto;
  left: 50%;
  transform: translateX(-50%);
  top: -40px;
  width: 150px;
  height: 150px;
  margin: 0 auto;
 }

 .detailed-section:nth-child(4) .star-badge-number {
  font-size: 40px;
  line-height: 55px;
 }

 .detailed-section:nth-child(4) .star-badge-text {
  font-size: 14px;
 }
}

/* Star Badge Popup */
.star-badge-popup {
 display: none;
 position: fixed;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
 background: #ffffff;
 border-radius: 50px;
 padding: 60px;
 max-width: 1000px;
 width: 90%;
 box-shadow: 3px 3px 6.5px rgba(0, 0, 0, 0.25);
 z-index: 1000;
}

.star-badge-popup.active {
 display: block;
}

/* Small screen fixes for popups */
@media (max-width: 500px) {
 .star-badge-popup {
  width: 85%;
  max-width: 300px;
  padding: 30px 25px;
  border-radius: 25px;
 }

 .popup-close {
  top: 15px;
  right: 15px;
  width: 20px;
  height: 20px;
 }

 .popup-number {
  font-size: 42px;
  line-height: 1.2;
  margin-bottom: 15px;
 }

 .popup-text {
  font-size: 18px;
  line-height: 1.4;
 }
}

.popup-close {
 position: absolute;
 top: 30px;
 left: 30px;
 width: 24px;
 height: 24px;
 flex-shrink: 0;
 aspect-ratio: 1/1;
 cursor: pointer;
}

.popup-close:hover {
 opacity: 0.8;
}

.popup-number {
 font-weight: 700;
 font-size: 196px;
 line-height: 1.39;
 margin-bottom: 20px;
}

.popup-number.blue {
 color: #7ab7d8;
}

.popup-number.purple {
 color: #8d8cc4;
}

/* Media query for smaller screens */
@media (max-width: 500px) {
 .popup-number.blue,
 .popup-number.purple,
 .popup-number {
  font-size: 130px;
 }
}

/* Additional media query to fix negative percentage values overflowing */
@media (max-width: 600px) {
 .popup-number.blue,
 .popup-number.purple,
 .popup-number {
  font-size: 90px;
  word-break: break-word;
  line-height: 1.2;
 }

 .star-badge-popup {
  padding: 40px;
 }

 .popup-text {
  font-size: 28px;
  line-height: 1.3;
 }
}

/* Further reduction for very small screens */
@media (max-width: 400px) {
 .popup-number.blue,
 .popup-number.purple,
 .popup-number {
  font-size: 70px;
 }

 .star-badge-popup {
  padding: 30px;
 }

 .popup-text {
  font-size: 22px;
 }
}

.popup-text {
 font-size: 34px;
 line-height: 1.14;
 color: #292b33;
 max-width: 800px;
}

.popup-overlay {
 display: none;
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background: rgba(0, 0, 0, 0.5);
 z-index: 999;
}

.popup-overlay.active {
 display: block;
}

/* Early Access Banner */
.early-access-banner {
 background-color: #8d8cc4;
 position: relative;
 width: 100vw;
 left: 50%;
 right: 50%;
 margin-left: -50vw;
 margin-right: -50vw;
 margin-top: 52px;
 margin-bottom: 52px;
 min-height: 380px;
 display: flex;
 flex-direction: column;
 justify-content: center;
 align-items: center;
 color: #ffffff;
 overflow: hidden;
}

.early-access-banner .banner-content {
 height: 100%;
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 width: 100%;
 overflow: visible;
 display: flex;
 align-items: center;
 justify-content: center;
}

.early-access-banner .slide {
 position: absolute;
 width: 100%;
 height: 100%;
 opacity: 0;
 transform: translateX(100%);
 transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1),
  opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
 display: flex;
 flex-direction: row;
 justify-content: center;
 align-items: center;
 padding: 0 100px 0 20px;
 box-sizing: border-box;
 will-change: transform, opacity;
 -webkit-backface-visibility: hidden;
 backface-visibility: hidden;
 -webkit-transform-style: preserve-3d;
 transform-style: preserve-3d;
 visibility: hidden;
}

.early-access-banner .slide.active {
 opacity: 1;
 transform: translateX(0);
 will-change: transform, opacity;
 visibility: visible;
}

.early-access-banner .slide.previous {
 opacity: 0;
 transform: translateX(-100%);
 will-change: transform, opacity;
 visibility: hidden;
}

.early-access-banner .slide-content {
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 width: 100%;
 z-index: 5;
}

.early-access-banner h3 {
 font-family: "Playfair Display", serif;
 font-size: 35px;
 font-style: italic;
 font-weight: 700;
 margin: 0 0 16px 0;
 text-align: center;
 width: 100%;
}

.early-access-banner p {
 font-size: 26px;
 margin: 0;
 text-align: center;
 width: 100%;
}

.early-access-banner .next-button {
 position: absolute;
 right: 20px;
 top: 50%;
 transform: translateY(-50%);
 width: 60px;
 height: 60px;
 border: none;
 border-radius: 10px;
 background: transparent;
 cursor: pointer;
 display: flex;
 align-items: center;
 justify-content: center;
 z-index: 10;
}

.early-access-banner .next-button img {
 width: 60px;
 height: 60px;
 filter: brightness(0) invert(1);
}

@media (min-width: 1400px) {
 .early-access-banner .slide {
  padding: 0 100px 0 20px;
 }
}

@media (max-width: 1399px) {
 .early-access-banner {
  min-height: 380px;
 }

 .early-access-banner .banner-content {
  min-height: 380px;
  display: flex;
  align-items: center;
  justify-content: center;
 }

 .early-access-banner .slide {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 0 80px 0 20px;
 }

 .early-access-banner h3 {
  font-size: 32px;
 }

 .early-access-banner p {
  font-size: 24px;
 }

 .early-access-banner .next-button {
  right: 20px;
  width: 50px;
  height: 50px;
 }

 .early-access-banner .next-button img {
  width: 50px;
  height: 50px;
 }
}

@media (max-width: 768px) {
 .early-access-banner {
  min-height: 320px;
 }

 .early-access-banner .slide {
  padding: 0 60px 0 20px;
  flex-direction: row;
 }

 .early-access-banner h3 {
  font-size: 28px;
  margin-bottom: 10px;
 }

 .early-access-banner p {
  font-size: 22px;
 }

 .early-access-banner .next-button {
  right: 15px;
  width: 40px;
  height: 40px;
 }

 .early-access-banner .next-button img {
  width: 40px;
  height: 40px;
 }
}

@media (max-width: 480px) {
 .early-access-banner {
  min-height: 250px;
 }

 .early-access-banner .slide {
  padding: 0 50px 0 15px;
  flex-direction: column;
  text-align: center;
 }

 .early-access-banner .slide-content {
  width: 100%;
  max-width: 100%;
 }

 .early-access-banner h3 {
  font-size: 22px;
  margin-bottom: 8px;
  width: 100%;
 }

 .early-access-banner p {
  font-size: 16px;
  width: 100%;
 }

 .early-access-banner .next-button {
  right: 10px;
  width: 30px;
  height: 30px;
 }

 .early-access-banner .next-button img {
  width: 30px;
  height: 30px;
 }
}

/* Remove horizontal cards styling */
.horizontal-cards-container,
.horizontal-cards-scroller,
.horizontal-card,
.horizontal-card .card-image,
.horizontal-card .card-content,
.horizontal-card .card-content h3,
.horizontal-card .card-divider,
.horizontal-card .card-text,
.horizontal-card .card-text ul,
.horizontal-card .card-text ul li,
.horizontal-card .card-text ul li:before {
 display: none;
}

/* Print styles */
@media print {
 .early-access-banner {
  break-inside: avoid;
 }
}

@media (max-width: 768px) {
 .section-header {
  flex-direction: row;
  align-items: baseline;
 }

 .section-number {
  font-size: clamp(32px, 5vw, 70px);
  margin-right: 15px;
 }

 .section-title {
  font-size: clamp(32px, 5vw, 70px);
 }

 .section-subtitle {
  font-size: clamp(18px, 2vw, 22px);
  margin-bottom: 40px;
 }
}

@media (max-width: 480px) {
 .section-header {
  flex-direction: row;
 }

 .section-number {
  font-size: clamp(24px, 4vw, 40px);
  margin-right: 10px;
 }

 .section-title {
  font-size: clamp(24px, 4vw, 40px);
 }

 .section-subtitle {
  font-size: clamp(16px, 1.8vw, 18px);
  margin-bottom: 30px;
 }
}

/* Special styling for Participation section (04) */
.detailed-section:nth-child(4) {
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 padding: 64px 0;
}

.detailed-section:nth-child(4) .section-header {
 margin-bottom: 40px;
 display: flex;
 flex-direction: row;
 flex-wrap: nowrap;
 align-items: baseline;
}

.detailed-section:nth-child(4) .section-number {
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-right: 20px;
}

.detailed-section:nth-child(4) .section-title {
 font-family: "Playfair Display", serif;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-bottom: 40px;
}

.detailed-section:nth-child(4) .section-subtitle {
 font-weight: 600;
 font-size: 65px;
 text-align: center;
 margin: 0 auto;
 color: white;
 position: absolute;
 bottom: 50px;
 left: 0;
 right: 0;
 width: 100%;
 z-index: 5;
 text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.detailed-section:nth-child(4) .section-subtitle em {
 font-style: italic;
}

.detailed-section:nth-child(4) .section-content {
 max-width: 1140px;
 margin: 0 auto;
 padding: 0 20px;
}

.detailed-section:nth-child(4) .section-content p {
 font-size: 20px;
 line-height: 1.6;
 color: #292b33;
 margin-bottom: 30px;
}

.detailed-section:nth-child(4) .section-content ul {
 list-style: none;
 margin: 0 0 40px 0;
 padding: 0;
}

.detailed-section:nth-child(4) .section-content ul li {
 font-size: 20px;
 line-height: 1.6;
 color: #292b33;
 margin-bottom: 12px;
 padding-left: 20px;
 position: relative;
}

.detailed-section:nth-child(4) .section-content ul li:before {
 content: "";
 position: absolute;
 left: 0;
 top: 12px;
 width: 6px;
 height: 6px;
 border-radius: 50%;
 background-color: #292b33;
}

.detailed-section:nth-child(4) .section-question {
 font-size: 35px;
 line-height: 1.4;
 font-weight: 500;
 text-align: center;
 margin: 80px auto 60px;
 max-width: 850px;
 color: #292b33;
 padding: 0 20px;
}

.detailed-section:nth-child(4) .section-question .key-text {
 font-size: 26px;
 font-style: normal;
 font-weight: 700;
 line-height: 32px;
 margin-bottom: 16px;
}

.detailed-section:nth-child(4) .section-question .main-text {
 font-size: 35px;
 font-style: normal;
 font-weight: 400;
 line-height: normal;
}

/* Add more responsive styles for the key section */
@media (max-width: 880px) {
 .detailed-section:nth-child(4) .section-question {
  font-size: 28px;
  line-height: 1.3;
  padding: 0 15px;
  max-width: 100%;
  margin: 60px auto 40px;
 }

 .detailed-section:nth-child(4) .section-question .key-text {
  font-size: 22px;
  line-height: 28px;
 }

 .detailed-section:nth-child(4) .section-question .main-text {
  font-size: 28px;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
 }

 .detailed-section:nth-child(4) .section-question br {
  display: none;
 }
}

@media (max-width: 640px) {
 .detailed-section:nth-child(4) .section-question {
  font-size: 22px;
  line-height: 1.3;
  padding: 0 10px;
 }

 .detailed-section:nth-child(4) .section-question .key-text {
  font-size: 20px;
  line-height: 24px;
 }

 .detailed-section:nth-child(4) .section-question .main-text {
  font-size: 22px;
  line-height: 1.3;
 }
}

@media (max-width: 480px) {
 .detailed-section:nth-child(4) .section-question {
  font-size: 18px;
  margin: 40px auto 30px;
 }

 .detailed-section:nth-child(4) .section-question .key-text {
  font-size: 18px;
  line-height: 22px;
  margin-bottom: 10px;
 }

 .detailed-section:nth-child(4) .section-question .main-text {
  font-size: 18px;
  line-height: 1.4;
 }
}

/* Star badge styling */
.detailed-section:nth-child(4) .star-badge {
 position: absolute;
 top: -40px;
 right: 95px;
 z-index: 10;
 width: 209px;
 height: 205px;
}

.detailed-section:nth-child(4) .star-badge-content {
 position: relative;
 z-index: 1;
 color: white;
}

.detailed-section:nth-child(4) .star-badge-number {
 font-weight: 700;
 font-size: 55px;
 line-height: 76px;
}

.detailed-section:nth-child(4) .star-badge-text {
 font-size: 16px;
 line-height: 16px;
}

/* Remove all filters from the Retention section star badge */
.detailed-section:nth-child(5) .star-badge img {
 filter: none !important;
}

/* Make sure text color is white for both star badges */
.detailed-section:nth-child(4) .star-badge-number,
.detailed-section:nth-child(4) .star-badge-text,
.detailed-section:nth-child(5) .star-badge-number,
.detailed-section:nth-child(5) .star-badge-text {
 color: white;
}

/* Question styling */
.detailed-section:nth-child(4) .section-question {
 font-size: 35px;
 line-height: 1.17;
 font-weight: 400;
 text-align: center;
 margin: 80px auto 60px;
 max-width: 850px;
 color: #292b33;
}

.detailed-section:nth-child(4) .section-content p,
.detailed-section:nth-child(4) .section-content ul {
 font-size: 20px;
 line-height: 1.3;
 margin-bottom: 30px;
}

/* Adjust the star badge content size */
.detailed-section:nth-child(4) .star-badge-number,
.detailed-section:nth-child(5) .star-badge-number {
 font-size: 32px;
 line-height: 36px;
 font-weight: bold;
}

.detailed-section:nth-child(4) .star-badge-text,
.detailed-section:nth-child(5) .star-badge-text {
 font-size: 14px;
 line-height: 16px;
}

/* Responsive adjustments for star badges */
@media (max-width: 1200px) {
 .detailed-section:nth-child(4) .star-badge,
 .detailed-section:nth-child(5) .star-badge {
  right: 40px;
  top: -20px;
  width: 150px;
  height: 150px;
 }
}

@media (max-width: 991px) {
 .detailed-section:nth-child(4) .star-badge,
 .detailed-section:nth-child(5) .star-badge {
  right: 30px;
  top: -20px;
  width: 130px;
  height: 130px;
 }

 .detailed-section:nth-child(4) .star-badge-number,
 .detailed-section:nth-child(5) .star-badge-number {
  font-size: 28px;
  line-height: 32px;
 }
}

@media (max-width: 767px) {
 .detailed-section:nth-child(4) .star-badge {
  right: 20px;
  top: -70px;
  width: 110px;
  height: 110px;
 }

 .detailed-section:nth-child(5) .star-badge {
  top: -70px;
 }

 .detailed-section:nth-child(4) .star-badge-number,
 .detailed-section:nth-child(5) .star-badge-number {
  font-size: 24px;
  line-height: 28px;
 }

 .detailed-section:nth-child(4) .star-badge-text,
 .detailed-section:nth-child(5) .star-badge-text {
  font-size: 12px;
  line-height: 14px;
 }

 .detailed-section:nth-child(4) .section-subtitle {
  font-size: 30px;
 }
}

/* Make sure content has proper spacing */
.detailed-section:nth-child(4) .section-content:first-of-type {
 margin-top: 60px;
}

/* Special styling for Retention section (05) */
.detailed-section:nth-child(5) {
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 padding: 64px 0;
}

.detailed-section:nth-child(5) .section-header {
 margin-bottom: 40px;
 display: flex;
 flex-direction: row;
 flex-wrap: nowrap;
 align-items: baseline;
}

.detailed-section:nth-child(5) .section-number {
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-right: 20px;
}

.detailed-section:nth-child(5) .section-title {
 font-family: "Playfair Display", serif;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-bottom: 40px;
}

.detailed-section:nth-child(5) .section-subtitle {
 color: #292b33;
 text-align: center;
 font-size: 35px;
 font-style: normal;
 font-weight: 400;
 line-height: normal;
 position: relative;
 margin: 40px auto 60px;
 width: 100%;
 z-index: 5;
}

.detailed-section:nth-child(5) .section-subtitle em {
 font-style: italic;
}

.detailed-section:nth-child(5) .content-with-badge {
 position: relative;
 width: 100%;
 max-width: 100%;
 margin: 50px 0 0;
 padding: 0;
}

/* Images in Retention section */
.detailed-section:nth-child(5) .images-grid {
 margin-top: 0;
 position: relative;
 margin-bottom: 30px;
 max-width: 100%;
 width: 100%;
 margin-left: auto;
 margin-right: auto;
 display: flex;
}

.detailed-section:nth-child(5) .retention-images {
 display: grid;
 grid-template-columns: 1fr 1fr;
 gap: 0;
 height: 600px;
 overflow: hidden;
 margin-bottom: 0;
}

.detailed-section:nth-child(5) .images-grid img {
 border-radius: 0;
 height: 100%;
 width: 100%;
 display: block;
 object-fit: cover;
}

/* Star badge in Retention section */
.detailed-section:nth-child(5) .star-badge {
 position: absolute;
 top: -20px;
 right: 50px;
 z-index: 10;
 width: 170px;
 height: 170px;
}

/* Change colors of the badges */
.detailed-section:nth-child(5) .star-badge img {
 filter: none !important;
}

/* Override any other color styles that might be applied */
.detailed-section:nth-child(5) .star-badge-number,
.detailed-section:nth-child(5) .star-badge-text {
 color: white;
}

/* Override popup star badge color */
.popup-number.purple {
 color: #8d8cc4;
}

/* Responsive styles for Retention section */
@media (max-width: 1200px) {
 .detailed-section:nth-child(5) .star-badge {
  right: 50px;
  top: -40px;
 }
}

@media (max-width: 991px) {
 .detailed-section:nth-child(5) .star-badge {
  right: 30px;
  top: -40px;
 }

 .detailed-section:nth-child(5) .section-subtitle {
  font-size: 50px;
 }
}

@media (max-width: 767px) {
 .detailed-section:nth-child(5) .star-badge {
  right: 15px;
  top: -40px;
  width: 100px;
  height: 100px;
 }

 .detailed-section:nth-child(5) .section-subtitle {
  font-size: 40px;
 }
}

@media (max-width: 480px) {
 .detailed-section:nth-child(5) .star-badge {
  right: auto;
  left: 50%;
  transform: translateX(-50%);
  top: -70px;
  width: 100px;
  height: 100px;
  margin: 0 auto;
 }

 .detailed-section:nth-child(5) .star-badge-number {
  font-size: 40px;
  line-height: 55px;
 }

 .detailed-section:nth-child(5) .star-badge-text {
  font-size: 14px;
 }

 .detailed-section:nth-child(5) .section-subtitle {
  font-size: 30px;
 }
}

/* Special styling for Consideration section (02) */
.detailed-section:nth-child(2) {
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 padding: 64px 0;
}

.detailed-section:nth-child(2) .section-header {
 margin-bottom: 40px;
 display: flex;
 flex-direction: row;
 flex-wrap: nowrap;
 align-items: baseline;
}

.detailed-section:nth-child(2) .section-number {
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-right: 20px;
}

.detailed-section:nth-child(2) .section-title {
 font-family: "Playfair Display", serif;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-bottom: 40px;
}

/* Consideration layout */
.consideration-layout {
 display: flex;
 flex-direction: row;
 max-width: 1440px;
 margin: 0 auto 80px;
 position: relative;
}

.consideration-text {
 flex: 1;
 padding: 0 20px;
 max-width: 600px;
}

.consideration-subtitle {
 font-size: 32px;
 font-weight: 500;
 color: #292b33;
 margin-bottom: 24px;
 line-height: 1.2;
}

.consideration-subtitle em {
 font-style: italic;
 font-family: "Playfair Display", serif;
}

.consideration-intro {
 font-size: 20px;
 line-height: 1.6;
 color: #292b33;
 margin-bottom: 40px;
 max-width: 580px;
}

.consideration-image-container {
 flex: 1;
 height: 600px;
 overflow: hidden;
}

.consideration-image {
 width: 100%;
 height: 100%;
 object-fit: cover;
}

.consideration-question {
 font-size: 32px;
 font-weight: 500;
 color: #292b33;
 margin: 40px 0;
 line-height: 1.2;
}

.consideration-question em {
 font-style: italic;
 font-family: "Playfair Display", serif;
}

/* Responsive styles for Consideration section */
@media (max-width: 1200px) {
 .consideration-layout {
  flex-direction: column;
 }

 .consideration-text {
  max-width: 100%;
  margin-bottom: 40px;
 }

 .consideration-image-container {
  height: 400px;
 }
}

@media (max-width: 768px) {
 .consideration-subtitle {
  font-size: 50px;
 }

 .consideration-question {
  font-size: 28px;
 }
}

@media (max-width: 480px) {
 .consideration-subtitle {
  font-size: 40px;
 }

 .consideration-question {
  font-size: 24px;
 }

 .consideration-image-container {
  height: 300px;
 }
}

/* Special styling for Enrollment section (03) */
.detailed-section:nth-child(3) {
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 padding: 64px 0;
}

.detailed-section:nth-child(3) .section-header {
 margin-bottom: 40px;
 display: flex;
 flex-direction: row;
 flex-wrap: nowrap;
 align-items: baseline;
}

.detailed-section:nth-child(3) .section-number {
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-right: 20px;
}

.detailed-section:nth-child(3) .section-title {
 font-family: "Playfair Display";
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-bottom: 40px;
}

.detailed-section:nth-child(3) .enrollment-subtitle {
 font-family: "Playfair Display";
 font-size: 26px;
 line-height: 1.2;
 font-weight: 400;
 margin-bottom: 30px;
 color: #292b33;
}

.detailed-section:nth-child(3) .enrollment-subtitle em {
 font-style: italic;
}

.detailed-section:nth-child(3) .enrollment-section {
 margin-bottom: 40px;
}

.detailed-section:nth-child(3) .enrollment-section p,
.detailed-section:nth-child(3) .enrollment-section ul {
 font-size: 20px;
 line-height: 1.3;
 margin-bottom: 30px;
 color: #292b33;
}

.detailed-section:nth-child(3) .enrollment-section ul {
 padding-left: 20px;
}

.detailed-section:nth-child(3) .enrollment-section li {
 margin-bottom: 10px;
}

.detailed-section:nth-child(3) .enrollment-images-grid {
 display: grid;
 grid-template-columns: repeat(4, 1fr);
 gap: 20px;
 max-width: 1140px;
 margin: 0 auto;
}

.detailed-section:nth-child(3) .enrollment-images-grid img {
 width: 100%;
 height: auto;
 display: block;
 border-radius: 0;
 object-fit: cover;
}

@media (max-width: 1200px) {
 .detailed-section:nth-child(3) .enrollment-images-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
 }
}

@media (max-width: 767px) {
 .detailed-section:nth-child(3) .section-subtitle {
  font-size: 22px;
 }

 .detailed-section:nth-child(3) .enrollment-images-grid {
  grid-template-columns: 1fr;
 }
}

/* Special styling for Advocacy section (06) */
.detailed-section:nth-child(6) {
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 padding: 64px 0;
 overflow: visible;
}

.detailed-section:nth-child(6) .section-header {
 display: none; /* Hide the original header since we're using custom one */
}

.detailed-section:nth-child(6) .section-header-advocacy {
 display: flex;
 flex-direction: row;
 align-items: baseline;
 margin-bottom: 40px;
}

.detailed-section:nth-child(6) .section-number {
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-right: 20px;
}

.detailed-section:nth-child(6) .section-title {
 font-family: "Playfair Display", serif;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin: 0;
}

.detailed-section:nth-child(6) .advocacy-subtitle {
 display: flex;
 width: 846px;
 height: 93px;
 flex-direction: column;
 justify-content: center;
 flex-shrink: 0;
 margin: 0 auto 40px;
 color: #292b33;
 text-align: center;
 font-size: 35px;
 font-style: normal;
 font-weight: 400;
 line-height: normal;
 max-width: 100%;
 padding: 0 20px;
 word-wrap: break-word;
 overflow-wrap: break-word;
}

.detailed-section:nth-child(6) .section-question {
 font-family: "Playfair Display", serif;
 font-size: 30px;
 line-height: 1.3;
 margin: 0 0 20px 0;
 color: #292b33;
}

.detailed-section:nth-child(6) .advocacy-section {
 max-width: 1140px;
 margin: 0 auto;
 padding: 0 20px;
 margin-bottom: 24px;
}

.detailed-section:nth-child(6) .advocacy-section p {
 font-size: 18px;
 line-height: 1.6;
 margin-bottom: 20px;
 color: #292b33;
 text-align: left;
}

.detailed-section:nth-child(6) .additional-image {
 width: 100%;
 max-width: 1140px;
 margin: 40px auto 0;
 padding: 0 20px;
}

.detailed-section:nth-child(6) .additional-image img {
 width: 100%;
 height: auto;
 object-fit: cover;
 border-radius: 8px;
}

/* Special styling for Awareness section (01) */
.detailed-section:nth-child(1) {
 position: relative;
 max-width: 1440px;
 margin: 0 auto;
 padding: 64px 0;
}

.detailed-section:nth-child(1) .section-header {
 margin-bottom: 40px;
 display: flex;
 flex-direction: row;
 flex-wrap: nowrap;
 align-items: baseline;
}

.detailed-section:nth-child(1) .section-number {
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-right: 20px;
}

.detailed-section:nth-child(1) .section-title {
 font-family: "Playfair Display", serif;
 font-weight: 400;
 font-size: 95px;
 line-height: 0.95;
 margin-bottom: 40px;
}

.detailed-section:nth-child(1) .section-content {
 max-width: 1140px;
 margin: 0 auto;
 padding: 0 20px;
}

.detailed-section:nth-child(1) .section-content p {
 font-size: 20px;
 line-height: 1.6;
 margin-bottom: 30px;
 color: #292b33;
}

.detailed-section:nth-child(1) .section-content p:last-child {
 margin-bottom: 0;
}

/* Add spacing between paragraphs */
.detailed-section:nth-child(1) .section-content p + p {
 margin-top: 30px;
}

/* Ensure line breaks work properly */
.detailed-section:nth-child(1) .section-content br {
 display: block;
 content: "";
 margin-top: 0;
}

.detailed-section:nth-child(6) .advocacy-subtitle em {
 font-family: "Playfair Display";
 font-size: 35px;
 font-style: italic;
 font-weight: 700;
 line-height: normal;
}

.detailed-section:nth-child(5) .retention-stats {
 display: flex;
 width: 100%;
 max-width: 1140px;
 min-height: 127px;
 flex-direction: column;
 justify-content: center;
 flex-shrink: 0;
 color: #292b33;
 text-align: center;
 font-size: 26px;
 font-style: normal;
 font-weight: 400;
 line-height: 1.3;
 margin: 0 auto 40px;
 padding: 0 20px;
 word-wrap: break-word;
 overflow-wrap: break-word;
}

@media (max-width: 1200px) {
 .detailed-section:nth-child(5) .retention-stats {
  font-size: 24px;
  min-height: auto;
  padding: 20px;
 }
}

@media (max-width: 880px) {
 .detailed-section:nth-child(5) .retention-stats {
  font-size: 22px;
  line-height: 1.4;
  padding: 15px;
  margin-bottom: 30px;
 }
}

@media (max-width: 640px) {
 .detailed-section:nth-child(5) .retention-stats {
  font-size: 20px;
  line-height: 1.5;
  padding: 10px;
 }
}

@media (max-width: 480px) {
 .detailed-section:nth-child(5) .retention-stats {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 25px;
 }
}

/* Advocacy Section Styles */
.advocacy-section {
 padding: 40px 20px;
 max-width: 1200px;
 margin: 0 auto;
}

.advocacy-card {
 background: #ffffff;
 border-radius: 10px;
 box-shadow: 0px 3px 6.5px rgba(0, 0, 0, 0.25);
 padding: 40px;
 margin-bottom: 30px;
}

.advocacy-content h2 {
 font-family: "Playfair Display", serif;
 font-size: 50px;
 line-height: 1.1;
 text-align: center;
 margin-bottom: 30px;
 font-weight: 400;
}

.advocacy-content ul {
 list-style: none;
 padding: 0;
 margin: 0;
 font-size: 26px;
 line-height: 1.23;
}

.advocacy-content li {
 margin-bottom: 15px;
 position: relative;
 padding-left: 25px;
}

.advocacy-content li::before {
 content: "•";
 position: absolute;
 left: 0;
 color: #000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
 .advocacy-content h2 {
  font-size: 36px;
 }

 .advocacy-content ul {
  font-size: 20px;
 }

 .advocacy-card {
  padding: 25px;
 }
}

/* Horizontal scrolling images */
.scrolling-images-container {
 width: 100%;
 max-width: 100%;
 margin: 0;
 padding: 0;
 overflow: hidden;
 position: relative;
}

.scrolling-images-wrapper {
 display: flex;
 transition: transform 0.5s ease;
 width: 200%;
 height: auto;
 min-height: 600px;
}

.scroll-image {
 width: 50%;
 height: auto;
 min-height: 600px;
 object-fit: contain;
 flex-shrink: 0;
 border-radius: 0;
 opacity: 0;
 visibility: hidden;
 transition: opacity 0.3s ease, visibility 0.3s ease;
 max-height: 80vh;
}

.scroll-image.visible {
 opacity: 1;
 visibility: visible;
}

.scroll-image:first-child {
 opacity: 1;
 visibility: visible;
}

.scrolling-images-wrapper.scrolled {
 transform: translateX(
  -50%
 ); /* Change to -50% since each image takes up 50% of wrapper */
}

.scrolling-images-wrapper.scrolled .scroll-image:last-child {
 opacity: 1;
 visibility: visible;
}

/* Match the Participation section image styles */
.detailed-section:nth-child(6) .scrolling-images-container {
 margin: 0;
 position: relative;
 margin-bottom: 30px;
 max-width: 100%;
 width: 100%;
}

.detailed-section:nth-child(6) .scroll-image {
 width: 50%;
 height: auto;
 min-height: 600px;
 display: block;
 object-fit: contain;
 max-height: 80vh;
}

/* Media queries for responsive images - match Participation section */
@media (max-width: 1200px) {
 .scroll-image {
  min-height: 500px;
 }
}

@media (max-width: 768px) {
 .scroll-image {
  min-height: 400px;
 }
}

@media (max-width: 480px) {
 .scroll-image {
  min-height: 300px;
 }
}

/* Remove horizontal cards styling */
.horizontal-cards-container,
.horizontal-cards-scroller,
.horizontal-card,
.horizontal-card .card-image,
.horizontal-card .card-content,
.horizontal-card .card-content h3,
.horizontal-card .card-divider,
.horizontal-card .card-text,
.horizontal-card .card-text ul,
.horizontal-card .card-text ul li,
.horizontal-card .card-text ul li:before {
 display: none;
}

.advocacy-section p:last-child {
 margin-bottom: 24px;
}

/* Add responsive styling for screens below 855px */
@media (max-width: 855px) {
 .section-header {
  flex-direction: column;
  align-items: flex-start;
  padding: 0 20px;
  width: 100%;
 }

 .section-title {
  font-size: clamp(32px, 5vw, 50px);
  line-height: 1.1;
  margin-bottom: 30px;
  width: 100%;
  display: block;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
 }

 .section-number {
  font-size: clamp(32px, 5vw, 50px);
  margin-bottom: 10px;
  display: block;
 }
}

/* Add specific styles for screens below 800px to keep title and number on one line */
@media (max-width: 800px) {
 .section-header {
  flex-direction: row;
  align-items: baseline;
  flex-wrap: nowrap;
 }

 .section-title {
  font-size: clamp(28px, 4vw, 36px);
  line-height: 1.1;
  margin-bottom: 30px;
  flex: 1;
  display: inline;
  white-space: normal;
  hyphens: none;
 }

 .section-number {
  font-size: clamp(28px, 4vw, 36px);
  line-height: 1.1;
  margin-right: 10px;
  margin-bottom: 0;
  display: inline-block;
  flex-shrink: 0;
 }

 .detailed-section .section-header {
  flex-direction: row !important;
 }

 .detailed-section .section-title {
  font-size: clamp(28px, 4vw, 36px) !important;
 }

 .detailed-section .section-number {
  font-size: clamp(28px, 4vw, 36px) !important;
 }
}

@media (max-width: 480px) {
 .section-header {
  padding: 0 10px;
 }

 .section-title {
  font-size: clamp(22px, 3.5vw, 28px);
  line-height: 1.2;
  margin-bottom: 20px;
 }

 .section-number {
  font-size: clamp(22px, 3.5vw, 28px);
  margin-right: 8px;
 }
}

/* Add responsive styles for the advocacy subtitle */
@media (max-width: 900px) {
 .detailed-section:nth-child(6) .advocacy-subtitle {
  width: 100%;
  height: auto;
  font-size: 30px;
  line-height: 1.3;
  margin-bottom: 30px;
  padding: 0 15px;
 }

 .detailed-section:nth-child(6) .advocacy-subtitle em {
  font-size: 30px;
  line-height: 1.3;
 }
}

@media (max-width: 768px) {
 .detailed-section:nth-child(6) .advocacy-subtitle {
  font-size: 26px;
  line-height: 1.3;
  margin-bottom: 25px;
 }

 .detailed-section:nth-child(6) .advocacy-subtitle em {
  font-size: 26px;
  line-height: 1.3;
 }
}

@media (max-width: 480px) {
 .detailed-section:nth-child(6) .advocacy-subtitle {
  font-size: 22px;
  line-height: 1.4;
  margin-bottom: 20px;
  padding: 0 10px;
 }

 .detailed-section:nth-child(6) .advocacy-subtitle em {
  font-size: 22px;
  line-height: 1.4;
 }
}

/* Media query for retention images grid under 1010px */
@media (max-width: 1010px) {
 .detailed-section:nth-child(5) .retention-images {
  height: auto;
 }

 .detailed-section:nth-child(5) .images-grid img {
 }
}

/* Mobile-friendly popup styles */
.star-badge-popup.mobile-friendly {
 max-width: 275px;
 padding: 25px 20px;
 border-radius: 20px;
}

.star-badge-popup.mobile-friendly .popup-close {
 top: 10px;
 right: 10px;
 width: 18px;
 height: 18px;
}

.star-badge-popup.mobile-friendly .popup-number {
 font-size: 38px;
 margin-bottom: 12px;
}

.star-badge-popup.mobile-friendly .popup-text {
 font-size: 16px;
 line-height: 1.3;
}
